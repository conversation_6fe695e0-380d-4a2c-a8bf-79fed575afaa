import * as HttpApi from "./http_api";
import { connectToGameWebsocket, exitRoom, sendJoinRoom, sendRequestSeat, onExitedRoomPromise } from "./custom_websocket";
import { GameError, User } from "./types";
import { AppType, CurrencyType, JobDataHandler, TableData, UserStatus, PlayerStatus, PlatformUserData, JobType } from "shared";
import { setLoggingContext, createLogger } from "./logging";
import { BattleRoomMsg } from "protobuf/MsgDeliverRespProto";
import * as GameLogic from "./game_logic";
import { CreateRoomParams } from "./http_api";
import { RoomMode } from "pkw";

const console = createLogger("FriendsMain");

type FriendsUrlConfig = {
    wpkHttpURL: string;
    wpkGameWsURL: string;
}

export type FriendsPlatformUser = User;
export type CreateFriendsRoomParams = CreateRoomParams;

export const FriendsMain = {
    urlConfig: undefined as FriendsUrlConfig,
    proxyUrl: undefined as string,

    init(urlConfig: FriendsUrlConfig, proxyUrl: string | undefined) {
        this.urlConfig = urlConfig;
        this.proxyUrl = proxyUrl;
    },

    async run(
        user: PlatformUserData,
        jobType: JobType,
        roomId: number,
        clubId: number | undefined,
        profileName: string | undefined,
        updateJobData: JobDataHandler,
        createRoomParams?: CreateRoomParams
    ): Promise<void> {

        setLoggingContext({ userId: user.userId, roomId });
        GameLogic.setJobDataHandler(updateJobData);
        HttpApi.setCurrentUser(user as FriendsPlatformUser);

        switch (jobType) {
            case JobType.PLAY:
                if (!roomId) {
                    if (createRoomParams) {
                        console.info(`Creating room for user ${user.userId}`, { userId: user.userId, createRoomParams });
                        const response = await HttpApi.createRoom(createRoomParams);
                        if (response.errorCode) {
                            throw new GameError("Cannot create room: " + response.errMsg, response.errorCode);
                        }

                        const roomsRecord = await HttpApi.getRoomsCreatedByUser();
                        roomId = roomsRecord.roomList[0].roomId as number;
                        console.info(`Created room ${roomId}`);
                    } else {
                        throw new GameError("Room ID or new room creation params required for play jobType");
                    }
                }
                await updateJobData({ status: UserStatus.loggedOn });
                await joinRoomAndPlay(user as FriendsPlatformUser, roomId, this.urlConfig.wpkGameWsURL, undefined);
                break;
            case JobType.SCAN:
                await scanPlayerTables(clubId, updateJobData);
                break;
            default:
                throw new Error(`Unknown jobType: ${jobType}`);
        }

        console.info(`Bot ${jobType} finished for user ${user.userId}`, { userId: user.userId, roomId });
    },

    async stop(): Promise<void> {
        await exitRoom();
    },
}

/*
 see CreateRoomParams
 room.playType 0=normal, 1=shortdeck, 2=omaha, 3=sng
*/
function toTableData(room: BattleRoomMsg): TableData {
    return {
        tableId: room.roomId as number,
        gameType: 'NLHE',
        gameId: -1,
        gameMode: 0, // 2 for multiflight tournaments
        roomMode: RoomMode.RoomModeNone, // TODO: or RoomMode.RoomModeBomb
        currency: CurrencyType.DIAMOND, // this is probably invalid and users don't use currency at all. probably depends on room.useWallet
        blinds: [room.gradeCfg.smallBlind, room.gradeCfg.bigBlind] as number[],
        straddle: room.isThirdBlind ? true : false,
        ante: room.gradeCfg.ante as number,
        playersCount: (room as any).sitPersonNum, // field not in protobuf
        maxPlayers: room.gamePersonNum as number,
        leftSeats: +room.gamePersonNum - (room as any).sitPersonNum,
        tableName: room.title as string,
        appId: AppType.FRIENDS,
    };
}

async function joinRoomAndPlay(
    user: User, roomId, websocketBaseUrl, createRoomParams?: CreateRoomParams, seatNum?: number
): Promise<void> {
    console.log(`Joining room ${roomId} for user ${user.username}`, { user });
    GameLogic.setUserStatus(UserStatus.inLobby);
    let roomInfo = await HttpApi.checkUserIntoRoomByRoomId(roomId);
    console.log(`Checked into room ${roomId}, joining`, roomInfo);
    const encryptedGameAesKey = await HttpApi.generateGameAESKey(roomId);
    await connectToGameWebsocket(user, websocketBaseUrl, roomInfo.urlPath, roomInfo.roomId, encryptedGameAesKey);

    const room = await sendJoinRoom();
    GameLogic.setUserStatus(UserStatus.inRoom);

    try {
        const resp = await sitDown(room, seatNum);
        GameLogic.setUserStatus(UserStatus.satDown);
        console.log(`Successfully joined room ${roomId} with seat number ${resp.optSeatNum}`);
    } catch (error) {
        if (error && error.errorCode === 7007) {
            GameLogic.setUserStatus(PlayerStatus.ERROR);
            await exitRoom();
        }
        console.error("Error joining room: ", error);
    }

    return onExitedRoomPromise;
}

async function sitDown(room: BattleRoomMsg, seatNum: number | undefined): Promise<BattleRoomMsg> {
    if (!room.gamePersonNum) {
        throw new GameError("Room does not have gamePersonNum defined");
    }
    seatNum = seatNum || room.optSeatNum as number; // seat recommended by server
    if (!seatNum) {
        const occupiedSeats = room.sitUserList.map(user => +user.seatNum);
        let freeSeats: number[] = []

        // seatNum starts from 1
        for (let i = 1; i <= (room.gamePersonNum as number); i++) {
            if (!occupiedSeats.includes(i)) {
                freeSeats.push(i);
            }
        }
        seatNum = freeSeats.at(Math.floor(Math.random() * freeSeats.length));
    }

    console.log(`Requesting seat ${seatNum} in room ${room.roomId}`);
    const roomInfo = await sendRequestSeat(seatNum);
    return roomInfo;
}

/*
roomList does not contain clubId, but contains all rooms from all clubs and non-clubs for player,
so we need to filter it by clubName
 */
async function scanPlayerTables(club_id: number, jobDataHandler: JobDataHandler) {
    const clubs = await HttpApi.getPlayerClubs();
    console.log('Player clubs:', clubs);
    const currentClub = clubs.find(club => club.clubId == club_id);

    if (!currentClub) {
        console.error('Current club not found', null, { clubs, club_id });
        throw new Error(`Club with id ${club_id} not found`);
    }

    return new Promise((_, reject) => {
        updateClubRooms();
        const scanInterval = setInterval(updateClubRooms , 10_000);
        async function updateClubRooms() {
            try {
                const roomsRecord = await HttpApi.getRoomsCreatedByUser();
                console.log('Player rooms', { rooms: roomsRecord.roomList })
                const tables = roomsRecord.roomList.filter(room => room.clubName == currentClub.clubName).map(toTableData);
                await jobDataHandler({ tables, club_id });
            } catch (e) {
                console.error('Scan bot failed getting rooms list', e);
                clearInterval(scanInterval);
                reject(e);
            }
        }
    });
}
